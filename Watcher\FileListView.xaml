<?xml version="1.0" encoding="utf-8"?>
<UserControl
    x:Class="Watcher.FileListView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Watcher"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" d:DataContext="{d:DesignInstance Type=local:FileListViewModel}"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Orientation="Vertical">
            <StackPanel Orientation="Horizontal">
                <Button Content="选择文件夹"
                        Command="{x:Bind ViewModel.SelectFolderCommand}"
                        Margin="10,10,5,10"/>
                <Button Content="刷新"
                        Command="{x:Bind ViewModel.RefreshCommand}"
                        Margin="5,10,5,10"/>
                <Button Content="显示最新文件"
                        Command="{x:Bind ViewModel.ShowLatestFilesCommand}"
                        Margin="5,10,10,10"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal" Margin="10,0,10,5">
                <TextBlock Text="日期过滤:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <DatePicker x:Name="VideoDatePicker"
                            Date="{x:Bind ViewModel.SelectedDate, Mode=TwoWay, Converter={StaticResource DateTimeToDateTimeOffsetConverter}}"
                            IsEnabled="True"/>
            </StackPanel>
            <CheckBox x:Name="DisplayModeCheckBox"
                      Content="显示人类可读日期"
                      IsChecked="{x:Bind ViewModel.DisplayHumanReadableDate, Mode=TwoWay}"
                      Margin="10,0,10,5"/>
            <CheckBox x:Name="ShowAllFilesCheckBox"
                      Content="显示所有文件"
                      IsChecked="{x:Bind ViewModel.ShowAllFiles, Mode=TwoWay}"
                      Margin="10,0,10,5"/>
            <!-- 添加进度条 -->
            <ProgressBar x:Name="ConversionProgressBar" 
                         Visibility="Collapsed"
                         IsIndeterminate="False" 
                         Value="100"
                         Maximum="100"
                         Height="10"
                         Margin="10,0,10,5"/>
        </StackPanel>

        <ListBox Grid.Row="1"
                 ItemsSource="{x:Bind ViewModel.FilteredVideoItems, Mode=OneWay}"
                 SelectedItem="{x:Bind ViewModel.SelectedVideo, Mode=TwoWay}"
                 Margin="10">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <TextBlock Text="{Binding DisplayName}"/>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
    </Grid>
</UserControl>
