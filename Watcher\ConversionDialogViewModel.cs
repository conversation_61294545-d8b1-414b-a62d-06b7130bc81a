using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Watcher.Services;

namespace Watcher
{
    public partial class ConversionDialogViewModel : ObservableObject
    {
        private readonly string _folderPath;
        private CancellationTokenSource? _cancellationTokenSource;
        private List<string> _filesToConvert = new List<string>();

        [ObservableProperty]
        private string _statusMessage = "准备转换...";

        [ObservableProperty]
        private string _currentFileMessage = "";

        [ObservableProperty]
        private string _progressText = "";

        [ObservableProperty]
        private int _progress = 0;

        [ObservableProperty]
        private int _maxProgress = 100;

        [ObservableProperty]
        private bool _isIndeterminate = false;

        [ObservableProperty]
        private string _consoleOutput = "";

        [ObservableProperty]
        private bool _canStart = true;

        [ObservableProperty]
        private bool _canStop = false;

        [ObservableProperty]
        private bool _canClose = true;

        public IRelayCommand StartConversionCommand { get; }
        public IRelayCommand StopConversionCommand { get; }
        public IRelayCommand CloseDialogCommand { get; }

        public event EventHandler? RequestClose;

        public ConversionDialogViewModel(string folderPath)
        {
            _folderPath = folderPath;
            StartConversionCommand = new AsyncRelayCommand(StartConversionAsync);
            StopConversionCommand = new RelayCommand(StopConversion);
            CloseDialogCommand = new RelayCommand(CloseDialog);

            InitializeFileList();
        }

        private void InitializeFileList()
        {
            if (string.IsNullOrEmpty(_folderPath) || !Directory.Exists(_folderPath))
            {
                StatusMessage = "文件夹路径无效";
                return;
            }

            // 获取所有.dav和.mp4文件（排除已经转换的文件）
            _filesToConvert = Directory.GetFiles(_folderPath)
                .Where(file => {
                    var ext = Path.GetExtension(file).ToLower();
                    var name = Path.GetFileNameWithoutExtension(file);
                    return (ext == ".dav" || ext == ".mp4") && !name.EndsWith("Z");
                })
                .ToList();

            MaxProgress = _filesToConvert.Count;
            StatusMessage = $"找到 {_filesToConvert.Count} 个文件需要转换";
            ProgressText = $"0 / {_filesToConvert.Count}";

            if (_filesToConvert.Count == 0)
            {
                StatusMessage = "没有找到需要转换的文件";
                CanStart = false;
            }
        }

        private async Task StartConversionAsync()
        {
            if (_filesToConvert.Count == 0)
                return;

            CanStart = false;
            CanStop = true;
            CanClose = false;
            IsIndeterminate = false;
            Progress = 0;
            ConsoleOutput = "";
            StatusMessage = "正在转换...";

            _cancellationTokenSource = new CancellationTokenSource();
            var token = _cancellationTokenSource.Token;

            try
            {
                for (int i = 0; i < _filesToConvert.Count; i++)
                {
                    if (token.IsCancellationRequested)
                    {
                        StatusMessage = "转换已取消";
                        break;
                    }

                    var inputFile = _filesToConvert[i];
                    var fileName = Path.GetFileName(inputFile);
                    CurrentFileMessage = $"正在转换: {fileName}";
                    
                    var fileNameWithoutExt = Path.GetFileNameWithoutExtension(inputFile);
                    var outputFile = Path.Combine(_folderPath, $"{fileNameWithoutExt}Z.mp4");

                    // 如果输出文件已存在，则跳过
                    if (File.Exists(outputFile))
                    {
                        AppendConsoleOutput($"跳过已存在的文件: {fileName}");
                        Progress = i + 1;
                        ProgressText = $"{Progress} / {MaxProgress}";
                        continue;
                    }

                    // 执行FFmpeg命令
                    bool success = await ConvertVideoAsync(inputFile, outputFile, token);
                    
                    if (success && !token.IsCancellationRequested)
                    {
                        // 转换成功，删除原文件
                        try
                        {
                            File.Delete(inputFile);
                            AppendConsoleOutput($"转换完成并删除原文件: {fileName}");
                        }
                        catch (Exception ex)
                        {
                            AppendConsoleOutput($"删除原文件失败: {fileName} - {ex.Message}");
                        }
                    }

                    Progress = i + 1;
                    ProgressText = $"{Progress} / {MaxProgress}";
                }

                if (!token.IsCancellationRequested)
                {
                    StatusMessage = "转换完成";
                    CurrentFileMessage = "";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"转换过程中发生错误: {ex.Message}";
                AppendConsoleOutput($"错误: {ex.Message}");
            }
            finally
            {
                CanStart = true;
                CanStop = false;
                CanClose = true;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        private void StopConversion()
        {
            _cancellationTokenSource?.Cancel();
            StatusMessage = "正在停止转换...";
        }

        private void CloseDialog()
        {
            if (_cancellationTokenSource != null && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                _cancellationTokenSource.Cancel();
            }
            RequestClose?.Invoke(this, EventArgs.Empty);
        }

        private void AppendConsoleOutput(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            ConsoleOutput += $"[{timestamp}] {message}\n";
        }

        private async Task<bool> ConvertVideoAsync(string inputFile, string outputFile, CancellationToken cancellationToken)
        {
            try
            {
                // 从配置服务获取FFmpeg路径
                var configService = Ioc.Default.GetRequiredService<ConfigurationService>();
                var ffmpegPath = configService.GetFFmpegPath();
                
                if (!File.Exists(ffmpegPath))
                {
                    AppendConsoleOutput("FFmpeg未找到，请确保FFmpeg文件夹包含ffmpeg.exe");
                    return false;
                }

                var arguments = $"-v quiet -f h264 -r 25 -i \"{inputFile}\" -c:v copy -an \"{outputFile}\"";
                AppendConsoleOutput($"执行命令: ffmpeg {arguments}");

                var startInfo = new ProcessStartInfo
                {
                    FileName = ffmpegPath,
                    Arguments = arguments,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = new Process { StartInfo = startInfo };
                
                // 订阅输出事件
                process.OutputDataReceived += (sender, e) => {
                    if (!string.IsNullOrEmpty(e.Data))
                        AppendConsoleOutput($"输出: {e.Data}");
                };
                
                process.ErrorDataReceived += (sender, e) => {
                    if (!string.IsNullOrEmpty(e.Data))
                        AppendConsoleOutput($"错误: {e.Data}");
                };

                process.Start();
                process.BeginOutputReadLine();
                process.BeginErrorReadLine();
                
                // 等待进程完成或被取消
                while (!process.HasExited && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(100, cancellationToken);
                }

                if (cancellationToken.IsCancellationRequested && !process.HasExited)
                {
                    process.Kill();
                    AppendConsoleOutput("转换被用户取消");
                    return false;
                }

                await process.WaitForExitAsync();
                
                bool success = process.ExitCode == 0;
                if (!success)
                {
                    AppendConsoleOutput($"FFmpeg退出码: {process.ExitCode}");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                AppendConsoleOutput($"转换视频文件时发生错误: {ex.Message}");
                return false;
            }
        }
    }
}
