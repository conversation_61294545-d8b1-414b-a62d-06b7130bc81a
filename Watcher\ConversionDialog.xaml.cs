using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using System;

namespace Watcher
{
    public sealed partial class ConversionDialog : ContentDialog
    {
        public ConversionDialogViewModel ViewModel { get; }

        public ConversionDialog(string folderPath)
        {
            this.InitializeComponent();
            ViewModel = new ConversionDialogViewModel(folderPath);
            
            // 订阅关闭请求事件
            ViewModel.RequestClose += ViewModel_RequestClose;
            
            // 订阅控制台输出变化事件，自动滚动到底部
            ViewModel.PropertyChanged += ViewModel_PropertyChanged;
        }

        private void ViewModel_RequestClose(object? sender, EventArgs e)
        {
            this.Hide();
        }

        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // 当控制台输出更新时，自动滚动到底部
            if (e.PropertyName == nameof(ConversionDialogViewModel.ConsoleOutput))
            {
                // 在UI线程上执行滚动操作
                this.DispatcherQueue.TryEnqueue(() =>
                {
                    // 查找ScrollViewer并滚动到底部
                    var scrollViewer = FindChild<ScrollViewer>(this);
                    if (scrollViewer != null)
                    {
                        scrollViewer.ScrollToVerticalOffset(scrollViewer.ScrollableHeight);
                    }
                });
            }
        }

        // 辅助方法：查找子控件
        private T? FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < Microsoft.UI.Xaml.Media.VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = Microsoft.UI.Xaml.Media.VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childResult = FindChild<T>(child);
                if (childResult != null)
                    return childResult;
            }
            return null;
        }

        protected override void OnClosed(ContentDialogClosedEventArgs args)
        {
            // 清理事件订阅
            ViewModel.RequestClose -= ViewModel_RequestClose;
            ViewModel.PropertyChanged -= ViewModel_PropertyChanged;
            base.OnClosed(args);
        }
    }
}
