using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.DependencyInjection;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Windows.Storage.Pickers;
using Watcher.Services;

namespace Watcher
{
    public partial class FileListViewModel : ObservableObject
    {
        private string _selectedFolder = string.Empty;

        [ObservableProperty]
        private ObservableCollection<VideoItem> _filteredVideoItems = new ObservableCollection<VideoItem>();

        [ObservableProperty]
        private VideoItem? _selectedVideo;

        [ObservableProperty]
        private DateTime _selectedDate = DateTime.Today;

        [ObservableProperty]
        private bool _displayHumanReadableDate = false;

        [ObservableProperty]
        private bool _showAllFiles = false;

        // 标记是否为用户手动选择的日期
        private bool _isUserSelectedDate = false;

        public string SelectedFolder
        {
            get => _selectedFolder;
            set
            {
                if (_selectedFolder != value)
                {
                    _selectedFolder = value;
                    OnPropertyChanged();
                }
            }
        }

        private readonly ConfigurationService _configService;

        public FileListViewModel()
        {
            _configService = Ioc.Default.GetRequiredService<ConfigurationService>();
            SelectFolderCommand = new AsyncRelayCommand(SelectFolderAsync);
            RefreshCommand = new RelayCommand(Refresh);
            ShowLatestFilesCommand = new RelayCommand(ShowLatestFiles);
            OpenConversionDialogCommand = new RelayCommand(OpenConversionDialog);

            // 从配置文件加载上次打开的文件夹
            LoadInitialFolder();
        }

        public IAsyncRelayCommand SelectFolderCommand { get; }
        public IRelayCommand RefreshCommand { get; }
        public IRelayCommand ShowLatestFilesCommand { get; }
        public IRelayCommand OpenConversionDialogCommand { get; }

        private async Task SelectFolderAsync()
        {
            try
            {
                var folderPicker = new FolderPicker();
                var hwnd = (Application.Current as App)?.GetMainWindow()?.GetWindowHandle() ?? IntPtr.Zero;
                
                if (hwnd != IntPtr.Zero)
                {
                    WinRT.Interop.InitializeWithWindow.Initialize(folderPicker, hwnd);
                }
                
                folderPicker.SuggestedStartLocation = PickerLocationId.Desktop;
                folderPicker.FileTypeFilter.Add("*");
                
                var folder = await folderPicker.PickSingleFolderAsync();
                if (folder != null)
                {
                    SelectedFolder = folder.Path;

                    // 重置用户选择标记，允许自动设置日期
                    _isUserSelectedDate = false;

                    // 保存到配置文件
                    _configService.UpdateLastOpenedFolder(folder.Path);

                    await RefreshAsync();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"选择文件夹时出错: {ex.Message}");
            }
        }

        private void Refresh()
        {
            _ = RefreshAsync();
        }

        /// <summary>
        /// 显示最新文件（设置过滤器为最新日期）
        /// </summary>
        private void ShowLatestFiles()
        {
            if (string.IsNullOrEmpty(SelectedFolder) || !Directory.Exists(SelectedFolder))
                return;

            try
            {
                var videoExtensions = new[] { ".mp4",".dav" };
                var files = Directory.GetFiles(SelectedFolder)
                    .Where(f => videoExtensions.Contains(Path.GetExtension(f).ToLowerInvariant()))
                    .ToArray();

                var latestDate = GetLatestVideoFileDate(files);
                if (latestDate.HasValue)
                {
                    // 设置为最新日期并标记为用户选择
                    SelectedDate = latestDate.Value.Date;
                    _isUserSelectedDate = true;

                    // 确保显示所有文件开关关闭
                    ShowAllFiles = false;

                    Debug.WriteLine($"显示最新文件，设置日期为: {SelectedDate:yyyy-MM-dd}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"显示最新文件时出错: {ex.Message}");
            }
        }

        private async Task RefreshAsync()
        {
            if (string.IsNullOrEmpty(SelectedFolder) || !Directory.Exists(SelectedFolder))
            {
                Debug.WriteLine("选择的文件夹不存在");
                FilteredVideoItems.Clear();
                return;
            }

            try
            {
                FilteredVideoItems.Clear();

                var videoExtensions = new[] { ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv", ".dav" };
                var files = Directory.GetFiles(SelectedFolder)
                    .Where(f => videoExtensions.Contains(Path.GetExtension(f).ToLowerInvariant()))
                    .OrderBy(f => f)
                    .ToArray();

                if (files.Length == 0)
                {
                    Debug.WriteLine("文件夹中没有视频文件");
                    return;
                }

                // 只有在用户未手动选择日期时，才自动设置为最新视频文件的日期
                if (!_isUserSelectedDate)
                {
                    var latestDate = GetLatestVideoFileDate(files);
                    if (latestDate.HasValue)
                    {
                        SelectedDate = latestDate.Value.Date;
                        Debug.WriteLine($"自动设置日期为最新视频文件日期: {SelectedDate:yyyy-MM-dd}");
                    }
                }

                foreach (var file in files)
                {
                    var videoItem = new VideoItem(file, DisplayHumanReadableDate);

                    // 如果显示所有文件开关打开，则不进行日期过滤
                    if (ShowAllFiles || Tools.FilterByDateInFileName(videoItem.FileName, SelectedDate))
                    {
                        FilteredVideoItems.Add(videoItem);
                    }
                }

                if (ShowAllFiles)
                {
                    Debug.WriteLine($"刷新文件列表完成，显示所有文件，找到 {FilteredVideoItems.Count} 个文件");
                }
                else
                {
                    Debug.WriteLine($"刷新文件列表完成，过滤日期: {SelectedDate:yyyy-MM-dd}，找到 {FilteredVideoItems.Count} 个文件");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"刷新文件列表时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载视频文件
        /// </summary>
        public void LoadVideos()
        {
            _ = RefreshAsync();
        }

        /// <summary>
        /// 获取一组视频文件中的最新日期
        /// </summary>
        /// <param name="files">视频文件路径数组</param>
        /// <returns>最新视频文件的日期，如果没有有效日期则返回null</returns>
        private DateTime? GetLatestVideoFileDate(string[] files)
        {
            try
            {
                var allDates = files
                    .Select(file => Tools.ExtractDates(Path.GetFileName(file)))
                    .Where(dates => dates.startTime.HasValue)
                    .Select(dates => dates.startTime.Value.Date)
                    .Distinct()
                    .OrderByDescending(date => date)
                    .ToList();

                if (allDates.Any())
                {
                    return allDates.First();
                }

                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取最新视频文件日期时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从配置文件加载初始文件夹
        /// </summary>
        private void LoadInitialFolder()
        {
            try
            {
                var lastFolder = _configService.GetLastOpenedFolder();
                if (!string.IsNullOrEmpty(lastFolder) && Directory.Exists(lastFolder))
                {
                    SelectedFolder = lastFolder;
                    // 初始加载时重置用户选择标记，允许自动设置日期
                    _isUserSelectedDate = false;
                    Debug.WriteLine($"从配置文件加载文件夹: {lastFolder}");
                }
                else
                {
                    Debug.WriteLine("配置文件中无有效文件夹路径，保持为空");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载初始文件夹时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 播放下一个视频
        /// </summary>
        public void PlayNextVideo()
        {
            if (FilteredVideoItems.Count == 0)
                return;

            int currentIndex = SelectedVideo != null ? FilteredVideoItems.IndexOf(SelectedVideo) : -1;
            int nextIndex = (currentIndex + 1) % FilteredVideoItems.Count;
            SelectedVideo = FilteredVideoItems[nextIndex];
        }

        /// <summary>
        /// 打开转换对话框
        /// </summary>
        private async void OpenConversionDialog()
        {
            if (string.IsNullOrEmpty(SelectedFolder))
            {
                Debug.WriteLine("请先选择文件夹");
                return;
            }

            try
            {
                var dialog = new ConversionDialog(SelectedFolder);

                // 设置XamlRoot
                var mainWindow = (Application.Current as App)?.GetMainWindow();
                if (mainWindow?.Content is FrameworkElement element)
                {
                    dialog.XamlRoot = element.XamlRoot;
                }

                var result = await dialog.ShowAsync();

                // 转换完成后刷新文件列表
                await RefreshAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"打开转换对话框时发生错误: {ex.Message}");
            }
        }

        partial void OnSelectedDateChanged(DateTime value)
        {
            // 标记为用户手动选择的日期
            _isUserSelectedDate = true;
            Debug.WriteLine($"用户选择日期: {value:yyyy-MM-dd}");
            _ = RefreshAsync();
        }

        partial void OnDisplayHumanReadableDateChanged(bool value)
        {
            foreach (var item in FilteredVideoItems)
            {
                item.UpdateDisplayName(value);
            }
        }

        partial void OnShowAllFilesChanged(bool value)
        {
            Debug.WriteLine($"显示所有文件开关变更为: {value}");
            _ = RefreshAsync();
        }
    }

    public class VideoItem : ObservableObject
    {
        private string _displayName;
        private readonly string _filePath;
        private readonly bool _isHumanReadable;

        public string FilePath => _filePath;
        public string FileName => Path.GetFileName(_filePath);
        public string DisplayName => _displayName;
        public string FullPath => _filePath; // 添加Fullpath属性

        public VideoItem(string filePath, bool isHumanReadable)
        {
            _filePath = filePath;
            _isHumanReadable = isHumanReadable;
            _displayName = GenerateDisplayName();
        }

        private string GenerateDisplayName()
        {
            var fileName = Path.GetFileName(_filePath);
            if (_isHumanReadable)
            {
                var (startTime, _) = Tools.ExtractDates(fileName);
                if (startTime.HasValue)
                {
                    return $"{startTime.Value:yyyy-MM-dd HH:mm:ss} - {fileName}";
                }
            }
            return fileName;
        }

        public void UpdateDisplayName(bool isHumanReadable)
        {
            _displayName = GenerateDisplayName();
            OnPropertyChanged(nameof(DisplayName));
        }
    }
}